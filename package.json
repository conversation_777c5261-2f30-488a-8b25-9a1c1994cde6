{"$schema": "https://json.schemastore.org/package", "name": "shenma-survey-manage-sys", "version": "1.0.0", "description": "神马调查管理系统", "author": "l<PERSON><PERSON><PERSON>", "license": "MIT", "type": "module", "scripts": {"dev": "vite serve --mode development", "test": "vite serve --mode test", "prod": "vite serve --mode production", "build:prod": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode production", "build:dev": "vite build --mode development", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode test", "preview": "vite preview", "lint:eslint": "eslint", "lint:eslint:fix": "eslint --fix", "prettier": "prettier --write ."}, "repository": {"type": "git", "url": "https://gitee.com/JavaLionLi/plus-ui.git"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ckpack/vue-color": "^1.6.0", "@element-plus/icons-vue": "2.3.1", "@highlightjs/vue-plugin": "2.1.0", "@kjgl77/datav-vue3": "^1.7.4", "@originjs/vite-plugin-commonjs": "^1.0.3", "@terraformer/wkt": "^2.2.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "12.7.0", "@zanmato/vue3-treeselect": "^0.4.1", "animate.css": "4.1.1", "await-to-js": "3.0.0", "axios": "1.7.8", "clipboard": "^2.0.11", "crypto-js": "4.2.0", "docx-preview": "^0.3.5", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "element-plus": "2.8.8", "esri-loader": "^3.7.0", "exif-js": "^2.3.0", "file-saver": "2.0.5", "fuse.js": "^7.1.0", "highlight.js": "11.9.0", "html2canvas": "^1.4.1", "image-conversion": "2.1.1", "js-base64": "^3.7.7", "js-beautify": "^1.15.4", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "json-bigint": "^1.0.0", "json-editor-vue3": "^1.1.1", "lodash-es": "^4.17.21", "mammoth": "^1.9.0", "mathjs": "^14.5.0", "monaco-editor": "^0.52.2", "monaco-editor-vue": "^1.0.10", "nprogress": "0.2.0", "pinia": "^2.1.6", "proj4": "^2.15.0", "qrcode": "^1.5.4", "screenfull": "6.0.2", "shapefile": "^0.6.6", "sortablejs": "^1.15.6", "spark-md5": "^3.0.2", "terraformer-wkt-parser": "^1.2.1", "transliteration": "^2.3.5", "uuid": "^11.1.0", "vite-plugin-monaco-editor": "^1.1.0", "vue": "^3.3.4", "vue-cropper": "1.1.4", "vue-esign": "^1.1.4", "vue-json-pretty": "2.4.0", "vue-router": "^4.2.4", "vue-types": "5.1.3", "vue3-seamless-scroll": "^3.0.2", "vuedraggable": "^4.1.0", "vxe-table": "4.5.22", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify/json": "2.2.276", "@types/crypto-js": "4.2.2", "@types/file-saver": "2.0.7", "@types/js-cookie": "3.0.6", "@types/node": "^22.13.4", "@types/nprogress": "0.2.3", "@unocss/preset-attributify": "66.0.0", "@unocss/preset-icons": "66.0.0", "@unocss/preset-uno": "66.0.0", "@vitejs/plugin-vue": "^4.3.4", "@vue/compiler-sfc": "^3.3.4", "@vue/eslint-config-prettier": "10.2.0", "@vue/eslint-config-typescript": "14.4.0", "autoprefixer": "10.4.20", "cross-env": "^7.0.3", "eslint": "9.21.0", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-vue": "9.32.0", "globals": "16.0.0", "prettier": "3.5.2", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.84.0", "terser": "^5.39.2", "typescript": "^5.0.2", "unocss": "66.0.0", "unplugin-auto-import": "0.17.5", "unplugin-icons": "0.18.5", "unplugin-vue-components": "28.0.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "^4.4.9", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons-ng": "^1.2.2", "vite-plugin-vue-devtools": "7.7.1", "vitest": "3.0.5", "vue-tsc": "^2.2.2"}, "engines": {"node": ">=18.18.0", "npm": ">=8.9.0"}, "browserslist": ["Chrome >= 87", "Edge >= 88", "Safari >= 14", "Firefox >= 78"]}