<!-- 筛选数据弹窗 -->
<template>
  <div class="searchData-main" v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      :title="isManager ? '数据管理' : '数据选择'"
      v-model="searchDialogCopy"
      width="874px"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      :before-close="handleClose"
      border
    >
      <div class="dialog-search">
        <el-button type="primary" size="small" @click="searchBtn">筛选数据</el-button>
        <div style="color: red; margin-left: 12px" v-show="isManager">勾选父级节点,会把父级节点下的所有子级节点绑定的数据一起删除!!!</div>
        <div class="flex items-center ml-10px" v-show="isShowSearch">
          <span>已筛选</span>
          <span type="primary" @click="searchBtn" class="flex items-center" style="margin-left: 5px; color: #1890ff; cursor: pointer"
            ><span>展开</span>
            <el-icon>
              <ArrowDown></ArrowDown>
            </el-icon>
          </span>
        </div>
      </div>
      <!-- 自定义表头 -->
      <div class="tree-header">
        <div class="header-checkbox">
          <el-checkbox v-model="selectAll" :indeterminate="isIndeterminate" @change="handleSelectAll" />
        </div>
        <div class="header-index">序号</div>
        <div class="header-name">数据名称</div>
        <div class="header-time">创建时间</div>
      </div>

      <el-tree-v2
        ref="treeV2Ref"
        :data="treeData"
        :props="treeProps"
        :width="824"
        :height="445"
        show-checkbox
        :item-size="40"
        @node-click="handleTreeNodeClick"
        @check="handleTreeCheck"
        :default-checked-keys="defaultCheckedKeys"
        class="dialog-content"
      >
        <template #default="{ node, data }">
          <div class="tree-node-content" :class="{ 'load-more-node': data.isLoadMore }">
            <template v-if="data.isLoadMore">
              <span class="load-more-text">
                <el-icon class="loading-icon"><Loading /></el-icon>
                {{ data.parcelName }}
              </span>
            </template>
            <template v-else>
              <span class="node-index">{{ getNodeIndex(data) }}</span>
              <span class="node-name">
                {{ data.parcelName }}
                <el-icon v-if="data.hasChildren && !data.childrenLoaded" class="expand-icon">
                  <ArrowRight />
                </el-icon>
              </span>
              <span class="node-time">{{ formatDateType(data.createTime) }}</span>
            </template>
          </div>
        </template>
      </el-tree-v2>
      <div class="page">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="dialogSearch.pageNum"
          :page-sizes="[20, 50, 100, 200, 500, 1000]"
          :page-size="dialogSearch.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit" v-if="!isManager">确 定</el-button>
          <el-button type="danger" @click="submitDel" v-else>删除</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 筛选弹窗 -->
    <dataSearch
      @handleCloseShaixuan="handleCloseShaixuan"
      @submitSearch="submitSearch"
      :dialogSearch="dialogSearch"
      :shaixuanDialog="shaixuanDialog"
      @clearUser="clearUser"
      :taskList="taskList"
      @handleResetSearch="handleResetSearch"
      :moduleId="moduleIdPop"
      @editCondition="editCondition"
    ></dataSearch>
    <!-- 批量选择节点弹窗 -->
    <el-dialog
      title="批量选择节点"
      v-model="batchDialog"
      width="30%"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      :before-close="handleCloseBatch"
    >
      <el-tree :data="tree" :props="defaultPropsTree" show-checkbox check-strictly default-expand-all node-key="id" @check-change="handleCheckChange">
      </el-tree>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseBatch">取 消</el-button>
          <el-button type="primary" @click="submitBatch">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getPlaceList, selectRules, selectParcelOneList } from '@/api/modal';
import { operaParcel, findAsyncFileTemp } from '@/api/project';
import { getSearchTask } from '@/api/task';
import dataSearch from '@/components/dataSearch/index.vue';
import { isArray } from '@/utils/validate';
import { useProjectStore } from '@/store/modules/project';
import { useRoute } from 'vue-router';
import { formatDateType } from '@/utils/filters';
import { Loading, ArrowRight } from '@element-plus/icons-vue';
const projectStore = useProjectStore();
const route = useRoute();

// --- 接收父组件参数定义 ---
interface Props {
  searchDialog: boolean;
  zdList?: any[]; // 如果你有具体类型，可以替换 any
  isManager?: boolean;
  moduleIdPop?: string;
  ifTree?: boolean;
  isKJ?: boolean;
  ruleTree?: any[]; // 同样建议替换 any
  ruleIds?: any[];
}

// 提供默认值
const props = withDefaults(defineProps<Props>(), {
  searchDialog: false,
  zdList: () => [],
  isManager: false,
  moduleIdPop: '',
  ifTree: false,
  isKJ: false,
  ruleTree: () => [],
  ruleIds: () => []
});
// 搜索弹窗副本
const searchDialogCopy = computed(() => props.searchDialog);

// --- emit ---
const emit = defineEmits<{
  (e: 'getChooseData', list: any[]): void;
  (e: 'closeSearchDialog'): void;
}>();

// 获取模块id
const moduleId = computed(() => {
  return props.moduleIdPop || projectStore.proModuleId;
});

// --- 变量定义 ---
const fullscreenLoading = ref(false);
const parcelList = ref([]); //数据列表
const total = ref(0);
const multipleSelection: any = ref([]);
const dialogSearch: any = ref({
  //弹窗筛选
  areaCode: '', //行政区划
  createDate: '', //创建时间
  optUserId: '', //最后修改时间
  createUserId: '', //采集人
  updateUserId: '', //最后修改人
  taskId: '', //任务id
  allocation: '', //查询是否分配任务 如果选择了任务设置为true
  pageNum: 1,
  pageSize: 20,
  parcelName: '',
  moduleId: moduleId.value, // 模块id
  conditionFields: [], //字段查询
  ruleIds: [],
  parcelCode: null
});
const shaixuanDialog = ref(false); //筛选弹窗
const taskList = ref([]); //任务列表
const isShowSearch = ref(false); //是否展示筛选内容
const isInit = ref(true); //是否允许初始化，反显已选择的数据
const defaultProps = reactive({
  children: 'list',
  label: 'parcelName'
});
const childNode = ref([]); //选中的子集id集合
const batchDialog = ref(false); //批量选择节点弹窗
const tree = ref([]);
const defaultPropsTree = reactive({
  children: 'list',
  label: 'typeName'
});
const batchChooseList = ref([]); //批量选择节点集合
const checkTreeList = ref([]); //当前选中的树结点的数据

// --- 虚拟树相关变量 ---
const treeData = ref<any[]>([]); // 树形数据
const defaultCheckedKeys = ref<string[]>([]); // 默认选中的节点keys
const treeProps = reactive({
  value: 'id',
  label: 'parcelName',
  children: 'children'
});

// --- 表头相关变量 ---
const selectAll = ref(false); // 全选状态
const isIndeterminate = ref(false); // 半选状态

// --- ref ---
const treeRef = ref();
const treeV2Ref = ref();
watch(
  () => searchDialogCopy.value,
  (val) => {
    if (val) {
      multipleSelection.value = props.zdList;
      if (props.moduleIdPop) {
        dialogSearch.value.moduleId = props.moduleIdPop;
      } else {
        dialogSearch.value.moduleId = projectStore.proModuleId;
      }
      getData(dialogSearch.value);
      getTree();
    }
  },
  { deep: true }
);

// --- 方法定义 ---
const getTree = () => {
  selectRules({ moduleId: moduleId.value }).then((res) => {
    if (res.code == 200) {
      tree.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 提交筛选
const submit = () => {
  const map = new Map();
  const arr = multipleSelection.value.filter((v) => !map.has(v.id) && map.set(v.id, v));
  // 当多次进入页数的时候和切换分页显示的时候 会有重复数据，所以需要数组去重一下
  emit('getChooseData', arr);
  multipleSelection.value = [];
  isInit.value = true;
  // 清空虚拟树的选中状态
  if (treeV2Ref.value) {
    treeV2Ref.value.setCheckedKeys([]);
  }
  defaultCheckedKeys.value = [];
  //打印筛选完以后的数据
  console.log(arr, 'arr');
};

// 关闭筛选
const handleClose = () => {
  emit('closeSearchDialog');
  //弹窗筛选
  dialogSearch.value = {
    areaCode: '', //行政区划
    createDate: '', //创建时间
    updateDate: '', //最后修改时间
    createUserId: '', //采集人
    optUserId: '', //最后修改人
    taskId: '', //任务id
    allocation: '', //查询是否分配任务 如果选择了任务设置为true
    pageNum: 1,
    pageSize: 20,
    parcelName: '',
    moduleId: moduleId.value,
    conditionFields: [], //字段查询
    ruleIds: [],
    parcelCode: null,
    express: false //是否查询表达式异常的数据
  };
  multipleSelection.value = [];
  // 清空虚拟树的选中状态
  if (treeV2Ref.value) {
    treeV2Ref.value.setCheckedKeys([]);
  }
  defaultCheckedKeys.value = [];
  isInit.value = true;
};

/**
 * 获取数据
 * @param parmas 参数
 */
const getData = (parmas: any) => {
  fullscreenLoading.value = true;
  if (props.ifTree) {
    //只有管理的时候才需要ifTree
    parmas.ifTree = props.ifTree;
  }
  if (props.isKJ) {
    parmas.ifTree = false;
    parmas.needKj = true;
  }
  parmas.levelNum = 1;
  if (route.query.iscq) {
    //如果是拆迁，需要把安置房数据刨除掉
    const ruleIds = [];
    props.ruleTree.forEach((v) => {
      if (!v.typeName.includes('安置房小区名称')) {
        ruleIds.push(v.id);
      }
    });
    parmas.ruleIds = ruleIds;
    delete parmas.levelNum;
  }
  if (props.ruleIds && route.query.isAZF) {
    //房源销控专用 多个模块相同节点的数据查询 需要把moduleId删除
    delete parmas.moduleId;
    delete parmas.levelNum;
    parmas.ruleIds = props.ruleIds;
  }
  getPlaceList(parmas).then((res) => {
    fullscreenLoading.value = false;
    if (res.code == 200) {
      parcelList.value = res.data.list;
      total.value = res.data.total;
      // 转换为树形数据（只保留第一层，子节点懒加载）
      treeData.value = convertToTreeData(res.data.list);
      if (isInit.value) {
        // 设置默认选中的节点
        const selectedKeys = multipleSelection.value.map((item) => item.id);
        defaultCheckedKeys.value = selectedKeys;
        // 使用 nextTick 确保组件已渲染
        nextTick(() => {
          if (treeV2Ref.value && selectedKeys.length > 0) {
            treeV2Ref.value.setCheckedKeys(selectedKeys);
          }
        });
        isInit.value = false;
      }
      // 更新全选状态
      updateSelectAllState();
      // 请求结束 看queryParams里面的downLoadId是否有值 有值代表是需要下载未匹配到的数据
      if (parmas.downLoadId) {
        downloadNoMappingData(parmas.downLoadId);
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 导出未匹配的数据
 * @param id
 */
const downloadNoMappingData = (id) => {
  const params = {
    id: id,
    suffix: 'txt',
    fileName: '未匹配id',
    del: 1
  };
  findAsyncFileTemp(params).then((res) => {
    if (res.status == 200) {
      if (res.data.type == 'application/json') {
        //导出异常
        const read = new FileReader();
        read.readAsText(res.data, 'utf-8');
        let bugMsg = '';
        read.onload = (data) => {
          bugMsg = JSON.parse(data.currentTarget['result']).msg;
          ElMessage.error(JSON.parse(data.currentTarget['result']).msg);
          if (!bugMsg) {
            bugMsg = '未知异常';
          }
        };
      } else {
        //导出正常
        const name = decodeURI(res.headers['content-disposition']);
        const index = name.indexOf('=');
        const endFileName = name.substring(index + 1, name.length) || '未匹配数据.txt';
        const blob = new Blob([res.data], { type: 'text/plain' });

        // 针对ie浏览器
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(blob, endFileName);
        } else {
          //非ie浏览器
          const downloadElement = document.createElement('a');
          const href = window.URL.createObjectURL(blob); //常见下载的链接
          downloadElement.href = href;
          downloadElement.download = endFileName; //下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); //点击下载
          document.body.removeChild(downloadElement); //下载完成移除元素
          window.URL.revokeObjectURL(href); //释放blob对象
        }
      }
    }
  });
};

/**
 * 分页大小改变
 * @param val
 */
const handleSizeChange = (val) => {
  dialogSearch.value.pageSize = val;
  dialogSearch.value.pageNum = 1;
  submitSearch();
};

/**
 * 分页页码改变
 * @param val
 */
const handleCurrentChange = (val) => {
  dialogSearch.value.pageNum = val;
  submitSearch();
};

/**
 * 全选所有数据
 * @param val
 */
const handleSeletionAll = (val) => {
  if (val.length > 0) {
    // 全选
    val.forEach((item) => {
      item.delFlag = 1;
      if (item.list.length > 0) {
        const isLag = val.length > 0;
        handleSelectTableDel(item.list, isLag);
      }
      multipleSelection.value.push(item);
    });
  } else {
    //  非全选
    multipleSelection.value = [];
  }
};

/**
 * 单行选择
 * @param selection
 * @param row
 */
const handleSelectTable = (selection, row) => {
  // 如果 selected =true  代表选中节点，0 或者false 代表未选中节点
  const selected = selection.length && selection.indexOf(row) !== -1;
  if (selected) {
    // 选中
    row.delFlag = 1;
    row.list.map((item) => {
      if (item.list.length > 0) {
        handleSelectTableDel(item.list, selected);
      }
      nextTick(() => {
        checkTreeList.value.push(item.id);
        treeRef.value.setCheckedKeys(checkTreeList.value);
      });
      item.delFlag = 1;

      return item;
    });
    multipleSelection.value.push(row);
  } else {
    row.delFlag = 0;
    row.list.map((item) => {
      if (item.list.length > 0) {
        handleSelectTableDel(item.list, selected);
      }
      nextTick(() => {
        const index = checkTreeList.value.indexOf(item.id);
        checkTreeList.value.splice(index, 1);
        treeRef.value.setCheckedKeys(checkTreeList.value);
      });

      item.delFlag = 0;

      return item;
    });
    const ind = multipleSelection.value.indexOf(row);
    multipleSelection.value.splice(ind, 1);
  }
};

/**
 * 变量下级数据
 * @param list 数据列表
 * @param selected 是否选中
 */
const handleSelectTableDel = (list, selected) => {
  if (selected) {
    return list.map((ite) => {
      nextTick(() => {
        checkTreeList.value.push(ite.id);
        treeRef.value.setCheckedKeys(checkTreeList.value);
      });
      ite.delFlag = 1;
      if (ite.list.length > 0) {
        handleSelectTableDel(ite.list, ite);
      }
      return ite;
    });
  } else {
    list.map((ite) => {
      if (ite.list.length > 0) {
        handleSelectTableDel(ite.list, selected);
      }
      nextTick(() => {
        const index = checkTreeList.value.indexOf(ite.id);
        checkTreeList.value.splice(index, 1);
        treeRef.value.setCheckedKeys(checkTreeList.value);
      });
      ite.delFlag = 0;
      return ite;
    });
  }
};

/**
 * 关闭筛选弹窗
 */
const handleCloseShaixuan = () => {
  shaixuanDialog.value = false;
};

/**
 * 重置筛选
 */
const handleResetSearch = () => {
  dialogSearch.value = {
    areaCode: '', //行政区划
    createDate: '', //创建时间
    updateDate: '', //最后修改时间
    createUserId: '', //采集人
    optUserId: '', //最后修改人
    taskId: '', //任务id
    allocation: '', //查询是否分配任务 如果选择了任务设置为true
    pageNum: 1,
    pageSize: 10,
    parcelName: '',
    moduleId: moduleId.value,
    conditionFields: [], //字段查询
    express: false //是否查询表达式异常的数据
  }; //弹窗筛选
  isShowSearch.value = false;
  getData(dialogSearch.value);
  shaixuanDialog.value = false;
};

/**
 * 提交筛选条件
 * @param isMapping 是否需要下载
 */
const submitSearch = (isMapping?: number) => {
  if (isMapping == 1) {
    const downLoadId = (Math.random() + new Date().getTime()).toString(32).slice(0, 8);
    dialogSearch.value.downLoadId = downLoadId;
  } else {
    delete dialogSearch.value.downLoadId;
  }
  if (dialogSearch.value.parcelCode && !isArray(dialogSearch.value.parcelCode)) {
    dialogSearch.value.parcelCode = dialogSearch.value.parcelCode.split(',');
  }
  if (dialogSearch.value.taskId) {
    //选择了任务id 就要改变这个值
    dialogSearch.value.allocation = true;
  } else if (!dialogSearch.value.taskId) {
    dialogSearch.value.allocation = '';
  }
  if (dialogSearch.value.createDate && dialogSearch.value.createDate.length != 0) {
    dialogSearch.value.createTimeStart = dialogSearch.value.createDate[0];
    dialogSearch.value.createTimeEnd = dialogSearch.value.createDate[1];
  } else if (!dialogSearch.value.createDate) {
    dialogSearch.value.createTimeStart = '';
    dialogSearch.value.createTimeEnd = '';
  }
  if (dialogSearch.value.updateDate && dialogSearch.value.updateDate.length != 0) {
    dialogSearch.value.updateTimeStart = dialogSearch.value.updateDate[0];
    dialogSearch.value.updateTimeEnd = dialogSearch.value.updateDate[1];
  } else if (!dialogSearch.value.updateDate) {
    dialogSearch.value.updateTimeStart = '';
    dialogSearch.value.updateTimeEnd = '';
  }
  // 这一步非常重要，需要重新组装字段筛选条件
  // 重新组装筛选条件
  const conditionFields = [];
  const itemParmas = JSON.parse(JSON.stringify(dialogSearch.value));
  if (itemParmas.conditionFields && itemParmas.conditionFields.length != 0) {
    itemParmas.conditionFields.forEach((v, idx) => {
      conditionFields.push(v);
      // 条件取下一个的条件 最后一个不进入/
      if (idx != itemParmas.conditionFields.length - 1) {
        const obj = { type: 2, value: itemParmas.conditionFields[idx + 1].relation };
        conditionFields.push(obj);
      }
    });
    if (conditionFields.length != 0 && conditionFields[conditionFields.length - 1].type == 2) {
      //如果最后一个是条件 删除
      conditionFields.pop();
    }
    itemParmas.conditionFields = conditionFields;
  }
  getData(itemParmas);
  const val = dialogSearch.value;
  if (val.areaCode || val.createDate || val.optUserId || val.createUserId || val.updateUserId || val.taskId || val.parcelName) {
    isShowSearch.value = true;
  } else {
    isShowSearch.value = false;
  }
  shaixuanDialog.value = false;
};

/**
 * 清除选中的用户 1采集人 2最后更新人
 * @param type
 */
const clearUser = (type) => {
  if (type == 1) {
    dialogSearch.value.createUserId = '';
    dialogSearch.value.createUserName = '';
  } else {
    dialogSearch.value.optUserId = '';
    dialogSearch.value.optUserName = '';
  }
};

/**
 * 查询任务列表
 */
const getSearchTaskList = async () => {
  const params = {
    pageSize: 1000,
    pageNum: 1,
    pageType: 3,
    moduleId: dialogSearch.value.moduleId
  };
  if (props.moduleIdPop) {
    params.moduleId = props.moduleIdPop;
  }
  await getSearchTask(params).then((res) => {
    if (res.code == 200) {
      taskList.value = res.data.list;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 查询任务按钮
 */
const searchBtn = async () => {
  shaixuanDialog.value = true;
  getSearchTaskList();
};

/**
 * 删除数据提交按钮
 */
const submitDel = () => {
  if (multipleSelection.value.length == 0 && childNode.value.length == 0) {
    ElMessage.error('请选择您要删除的数据！！！');
    return;
  }
  ElMessageBox.confirm('确定要删除已选择的数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  })
    .then(() => {
      ElMessageBox.confirm('该操作会彻底删除数据，数据将无法找回，请谨慎操作！！！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        const list = [];
        const map = new Map();
        multipleSelection.value
          .filter((v) => !map.has(v.id) && map.set(v.id, v))
          .forEach((v) => {
            list.push({
              id: v.id,
              delFlag: 1,
              taskId: 0,
              ruleId: v.ruleId
            });
          });
        childNode.value.forEach((v) => {
          list.push({
            id: v.id,
            delFlag: 1,
            taskId: 0,
            ruleId: v.ruleId
          });
        });
        operaParcel(list).then((res) => {
          if (res.code == 200) {
            ElMessage({
              type: 'success',
              message: '删除成功!'
            });
            getData(dialogSearch.value);
            multipleSelection.value = [];
            // 清空虚拟树的选中状态
            if (treeV2Ref.value) {
              treeV2Ref.value.setCheckedKeys([]);
            }
            defaultCheckedKeys.value = [];
            childNode.value = [];
          } else {
            ElMessage.error(res.msg);
          }
        });
      });
    })
    .catch(() => {});
};

/**
 * 修改筛选条件
 * @param type 1新增 2删除
 * @param idx 删除的下标
 */
const editCondition = (type, idx) => {
  //type 1新增 2删除 idx 删除的下标
  if (type == 1) {
    dialogSearch.value.conditionFields.push({
      name: '', //字段名字
      operator: '=',
      value: '',
      index: '',
      relation: 'and', // 关系 and or
      type: 1, // 1字段 2关系
      linkId: undefined
    });
  } else {
    dialogSearch.value.conditionFields.splice(idx, 1);
  }
};

/**
 * 节点选中
 * @param data 节点数据
 * @param flg 是否选中
 */
const handleNodeClick = (data, flg) => {
  if (flg) {
    //选中
    const isHas = checkTreeList.value.indexOf(data.id) == -1;
    if (isHas) {
      //  不存在才推送
      checkTreeList.value.push(data.id);
      childNode.value.push({ id: data.id, ruleId: data.ruleId });
    }

    if (data.list.length > 0) {
      //  变量取消所有节点
      data.list.forEach((item) => {
        handleNodeClick(item, true);
      });
    }
    treeRef.value.setCheckedKeys(checkTreeList.value);
  } else {
    for (let i = 0; i < childNode.value.length; i++) {
      if (childNode.value[i].id == data.id) {
        childNode.value.splice(i, 1);
        break;
      }
    }
    // 取消反选
    data.delFlag = 0;
    const index = checkTreeList.value.indexOf(data.id);
    checkTreeList.value.splice(index, 1);
    if (data.list.length > 0) {
      data.list.forEach((item) => {
        handleNodeClick(item, false);
      });
    }
    treeRef.value.setCheckedKeys(checkTreeList.value);
  }
};

// 移除原有的表格行点击处理，现在使用树节点点击

/**
 * 批量选择树节点
 */
const batchNode = () => {
  if (multipleSelection.value.length == 0) {
    ElMessage.error('请至少选择一条数据！！！');
    return;
  }
  batchDialog.value = true;
};

/**
 * 关闭批量选择节点
 */
const handleCloseBatch = () => {
  batchDialog.value = false;
};

/**
 * 提交批量选择节点
 */
const submitBatch = () => {};

/**
 * 节点选中
 * @param data 节点数据
 * @param flg 是否选中
 */
const handleCheckChange = (data, flg) => {};

// 移除原有的表格列定义，虚拟树使用模板渲染

/**
 * 转换平铺数据为树形数据（只保留第一层，子节点懒加载）
 * @param list 平铺数据列表
 */
const convertToTreeData = (list: any[]) => {
  return list.map((item) => {
    const node = {
      ...item,
      id: item.id || `node-${Math.random().toString(36).substr(2, 9)}`,
      children: [],
      // 标记是否有子节点（用于显示展开图标）
      hasChildren: item.list && item.list.length > 0,
      // 标记是否已加载子节点
      childrenLoaded: false,
      // 设置层级，第一层为1
      levelNum: item.levelNum || 1
    };

    // 不预加载子节点，只在点击时加载
    return node;
  });
};

/**
 * 获取节点索引
 * @param data 节点数据
 */
const getNodeIndex = (data: any) => {
  const flatList = flattenTreeData(treeData.value);
  return flatList.findIndex((item) => item.id === data.id) + 1;
};

/**
 * 扁平化树形数据
 * @param treeData 树形数据
 */
const flattenTreeData = (treeData: any[]): any[] => {
  const result: any[] = [];
  const traverse = (nodes: any[]) => {
    nodes.forEach((node) => {
      result.push(node);
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(treeData);
  return result;
};

/**
 * 处理树节点点击
 * @param data 节点数据
 * @param node 节点对象
 */
const handleTreeNodeClick = (data: any, node: any) => {
  console.log('Tree node clicked:', data, node);

  // 检查是否是"加载更多"节点
  if (data.isLoadMore) {
    loadMoreChildren(data, node);
    return;
  }

  // 如果节点没有子节点或子节点为空，则请求子节点数据
  if (!data.children || data.children.length === 0) {
    loadChildren(data, node);
  }
};

/**
 * 处理树节点选中
 * @param data 节点数据
 * @param info 选中信息
 */
const handleTreeCheck = (data: any, info: any) => {
  multipleSelection.value = info.checkedNodes;
  updateSelectAllState();
  console.log('Tree checked:', data, info);
};

/**
 * 处理全选
 * @param val 是否全选
 */
const handleSelectAll = (val: boolean) => {
  console.log('handleSelectAll called with:', val);

  if (val) {
    // 全选：获取所有节点的keys
    const allKeys = getAllNodeKeys(treeData.value);
    console.log('All keys:', allKeys);

    // 使用 setCheckedKeys 方法设置选中状态
    if (treeV2Ref.value) {
      treeV2Ref.value.setCheckedKeys(allKeys);
    }
    multipleSelection.value = flattenTreeData(treeData.value);
  } else {
    // 取消全选：清空所有选中
    console.log('Clearing all selections');

    if (treeV2Ref.value) {
      treeV2Ref.value.setCheckedKeys([]);
    }
    multipleSelection.value = [];
  }

  // 手动更新状态，确保界面同步
  nextTick(() => {
    updateSelectAllState();
  });
};

/**
 * 获取所有节点的keys
 * @param nodes 节点数组
 */
const getAllNodeKeys = (nodes: any[]): string[] => {
  const keys: string[] = [];
  const traverse = (nodeList: any[]) => {
    nodeList.forEach((node) => {
      keys.push(node.id);
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(nodes);
  return keys;
};

/**
 * 更新全选状态
 */
const updateSelectAllState = () => {
  const totalNodes = flattenTreeData(treeData.value);
  const selectedCount = multipleSelection.value.length;
  const totalCount = totalNodes.length;

  console.log('updateSelectAllState:', { selectedCount, totalCount });

  if (selectedCount === 0) {
    selectAll.value = false;
    isIndeterminate.value = false;
  } else if (selectedCount === totalCount) {
    selectAll.value = true;
    isIndeterminate.value = false;
  } else {
    selectAll.value = false;
    isIndeterminate.value = true;
  }

  console.log('Updated state:', { selectAll: selectAll.value, isIndeterminate: isIndeterminate.value });
};

/**
 * 获取节点的子级数据（懒加载）
 * @param data 当前节点的数据信息
 * @param node 当前节点
 */
const loadChildren = async (data: any, node: any) => {
  console.log('Loading children for node:', data);

  // 如果已经加载过子节点，直接返回
  if (data.childrenLoaded) {
    return;
  }

  // 构建请求参数，使用与原始数据请求相同的参数结构
  const params = {
    ...dialogSearch.value,
    parentId: data.id, // 父节点ID
    levelNum: (data.levelNum || 1) + 1, // 层级加1
    pageNum: 1,
    pageSize: 20
  };

  try {
    const resp = await getPlaceList(params, { moudleId: params.moduleId });
    if (resp.code === 200) {
      // 转换子节点数据
      const childrenData = resp.data.list.map((item: any) => ({
        ...item,
        id: item.id || `node-${Math.random().toString(36).substr(2, 9)}`,
        children: [],
        hasChildren: item.list && item.list.length > 0,
        childrenLoaded: false,
        levelNum: (data.levelNum || 1) + 1
      }));

      // 设置子节点
      data.children = childrenData;
      data.childrenLoaded = true;
      data.total = resp.data.total;

      // 如果有更多数据，添加"加载更多"节点
      if (resp.data.total > 20) {
        data.children.push({
          parcelName: '加载更多',
          id: `${data.id}_loadmore`,
          isLoadMore: true,
          parentId: data.id,
          pageNum: 2,
          pageSize: 20
        });
      }

      // 更新树形数据以触发重新渲染
      treeData.value = [...treeData.value];

    } else {
      ElMessage.error(resp.msg);
    }
  } catch (error) {
    console.error('Error loading children:', error);
    ElMessage.error('加载子节点失败');
  }
};

/**
 * 加载更多子节点数据
 * @param data 加载更多节点的数据
 * @param node 加载更多节点
 */
const loadMoreChildren = async (data: any, node: any) => {
  console.log('Loading more children:', data);

  // 找到父节点
  const parentNode = findNodeById(treeData.value, data.parentId);
  if (!parentNode) {
    ElMessage.error('找不到父节点');
    return;
  }

  // 构建请求参数
  const params = {
    ...dialogSearch.value,
    parentId: data.parentId,
    levelNum: parentNode.levelNum + 1,
    pageNum: data.pageNum,
    pageSize: 20
  };

  try {
    const resp = await getPlaceList(params, { moudleId: params.moduleId });
    if (resp.code === 200) {
      // 移除"加载更多"节点
      const loadMoreIndex = parentNode.children.findIndex((child: any) => child.isLoadMore);
      if (loadMoreIndex !== -1) {
        parentNode.children.splice(loadMoreIndex, 1);
      }

      // 转换并添加新的子节点
      const newChildren = resp.data.list.map((item: any) => ({
        ...item,
        id: item.id || `node-${Math.random().toString(36).substr(2, 9)}`,
        children: [],
        hasChildren: item.list && item.list.length > 0,
        childrenLoaded: false,
        levelNum: parentNode.levelNum + 1
      }));

      parentNode.children.push(...newChildren);

      // 判断是否还需要"加载更多"节点
      const totalLoaded = parentNode.children.length;
      if (totalLoaded < parentNode.total) {
        parentNode.children.push({
          parcelName: '加载更多',
          id: `${data.parentId}_loadmore`,
          isLoadMore: true,
          parentId: data.parentId,
          pageNum: data.pageNum + 1,
          pageSize: 20
        });
      }

      // 更新树形数据以触发重新渲染
      treeData.value = [...treeData.value];

    } else {
      ElMessage.error(resp.msg);
    }
  } catch (error) {
    console.error('Error loading more children:', error);
    ElMessage.error('加载更多失败');
  }
};

/**
 * 根据ID查找节点
 * @param nodes 节点数组
 * @param id 节点ID
 */
const findNodeById = (nodes: any[], id: string): any => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

// 移除原有的表格行样式方法，虚拟树使用不同的样式处理
</script>
<style lang="scss" scoped>
.dialog-search {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.searchData-main {
  width: 100%;
  height: 100%;
}
.dialog-content {
  height: 485px;
  width: 100%;
  overflow: auto;
  /*滚动条样式*/
  &::-webkit-scrollbar {
    width: 10px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
  :deep(.el-table-v2__header) {
    background-color: var(--el-fill-color-light);
    border-bottom: 1px solid var(--el-border-color-lighter);

    .el-table-v2__header-cell {
      font-weight: 600;
      color: var(--el-text-color-primary);
      padding: 8px 12px;
    }
  }

  :deep(.el-table-v2__row) {
    transition: background-color 0.2s ease;
    border-bottom: 1px solid var(--el-border-color-lighter);

    &:hover {
      background-color: var(--el-fill-color-light);
    }

    &.selected-row {
      background-color: var(--el-color-primary-light-9);

      &:hover {
        background-color: var(--el-color-primary-light-8);
      }
    }
  }

  :deep(.el-checkbox) {
    .el-checkbox__inner {
      border-radius: 2px;
      transition: all 0.2s;
    }

    &.is-checked {
      .el-checkbox__inner {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }
    }
  }

  // 虚拟树样式
  :deep(.el-tree-v2) {
    .el-tree-node {
      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }
  }
}

// 表头样式
.tree-header {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  background-color: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-lighter);
  font-weight: 600;
  color: var(--el-text-color-primary);

  .header-checkbox {
    width: 55px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .header-index {
    width: 70px;
    text-align: center;
    padding: 8px 12px;
  }

  .header-name {
    flex: 1;
    margin-left: 12px;
    padding: 8px 12px;
  }

  .header-time {
    width: 299px;
    text-align: right;
    padding: 8px 12px;
  }
}

// 树节点内容样式
.tree-node-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;

  .node-index {
    width: 70px;
    text-align: center;
    color: var(--el-text-color-regular);
  }

  .node-name {
    flex: 1;
    margin-left: 12px;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;

    .expand-icon {
      font-size: 12px;
      color: var(--el-color-info);
      transition: transform 0.2s;

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  .node-time {
    width: 299px;
    text-align: right;
    color: var(--el-text-color-regular);
  }

  // 加载更多节点样式
  &.load-more-node {
    justify-content: center;
    color: var(--el-color-primary);
    cursor: pointer;

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }

    .load-more-text {
      display: flex;
      align-items: center;
      gap: 8px;

      .loading-icon {
        animation: rotate 1s linear infinite;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.page {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
