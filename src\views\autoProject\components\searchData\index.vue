<!-- 筛选数据弹窗 -->
<template>
  <div class="searchData-main" v-loading.fullscreen.lock="fullscreenLoading">
    <el-dialog
      :title="isManager ? '数据管理' : '数据选择'"
      v-model="searchDialogCopy"
      width="874px"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      :before-close="handleClose"
      border
    >
      <div class="dialog-search">
        <el-button type="primary" size="small" @click="searchBtn">筛选数据</el-button>
        <div style="color: red; margin-left: 12px" v-show="isManager">勾选父级节点,会把父级节点下的所有子级节点绑定的数据一起删除!!!</div>
        <div class="flex items-center ml-10px" v-show="isShowSearch">
          <span>已筛选</span>
          <span type="primary" @click="searchBtn" class="flex items-center" style="margin-left: 5px; color: #1890ff; cursor: pointer"
            ><span>展开</span>
            <el-icon>
              <ArrowDown></ArrowDown>
            </el-icon>
          </span>
        </div>
      </div>
      <el-tree-v2
        ref="treeV2Ref"
        :data="treeData"
        :props="treeProps"
        :width="824"
        :height="485"
        :item-size="40"
        @node-click="handleNodeClick"
        @check="handleTreeCheck"
        :default-checked-keys="defaultCheckedKeys"
        class="dialog-content"
      >
        <template #default="{ node, data }">
          <div
            class="tree-node-content"
            :class="[`level-${data.levelNum}`, { 'load-more-node': data.isLoadMore }]"
            :style="{ paddingLeft: `${(data.levelNum - 1) * 20 + 12}px` }"
          >
            <!-- 加载更多节点的特殊显示 -->
            <template v-if="data.isLoadMore">
              <span class="load-more-content">
                <el-icon class="loading-icon"><Loading /></el-icon>
                <span class="load-more-text">{{ data.parcelName }}</span>
              </span>
            </template>

            <!-- 普通节点显示 -->
            <template v-else>
              <!-- 层级连接线 -->
              <div class="level-lines">
                <div v-for="level in data.levelNum - 1" :key="level" class="level-line" :style="{ left: `${(level - 1) * 20 + 6}px` }"></div>
              </div>

              <!-- 复选框 -->
              <span class="node-checkbox">
                <el-checkbox
                  :model-value="isNodeChecked(data)"
                  @change="handleNodeCheck(data, $event)"
                />
              </span>

              <!-- 展开/收起图标 -->
              <span class="node-expand" v-if="canLoadChildren(data)">
                <el-icon class="expand-icon" :class="{ 'expanded': data.loaded && data.children.length > 0 }">
                  <ArrowRight />
                </el-icon>
              </span>
              <span class="node-expand-placeholder" v-else></span>

              <!-- 节点内容 -->
              <span class="node-index">{{ getNodeIndex(data) }}</span>
              <span class="node-name" :class="`level-${data.levelNum}`">
                {{ data.parcelName }}
              </span>
              <span class="node-time">{{ formatDateType(data.createTime) }}</span>
            </template>
          </div>
        </template>
      </el-tree-v2>
      <div class="page">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="dialogSearch.pageNum"
          :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
          :page-size="dialogSearch.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit" v-if="!isManager">确 定</el-button>
          <el-button type="danger" @click="submitDel" v-else>删除</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 筛选弹窗 -->
    <dataSearch
      @handleCloseShaixuan="handleCloseShaixuan"
      @submitSearch="submitSearch"
      :dialogSearch="dialogSearch"
      :shaixuanDialog="shaixuanDialog"
      @clearUser="clearUser"
      :taskList="taskList"
      @handleResetSearch="handleResetSearch"
      :moduleId="moduleIdPop"
      @editCondition="editCondition"
    ></dataSearch>
    <!-- 批量选择节点弹窗 -->
    <el-dialog
      title="批量选择节点"
      v-model="batchDialog"
      width="30%"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      :before-close="handleCloseBatch"
    >
      <el-tree :data="tree" :props="defaultPropsTree" show-checkbox check-strictly default-expand-all node-key="id" @check-change="handleCheckChange">
      </el-tree>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseBatch">取 消</el-button>
          <el-button type="primary" @click="submitBatch">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getPlaceList, selectRules, selectParcelOneList } from '@/api/modal';
import { operaParcel, findAsyncFileTemp } from '@/api/project';
import { ArrowRight, Loading } from '@element-plus/icons-vue';
import { getSearchTask } from '@/api/task';
import dataSearch from '@/components/dataSearch/index.vue';
import { isArray } from '@/utils/validate';
import { useProjectStore } from '@/store/modules/project';
import { useRoute } from 'vue-router';
import { formatDateType } from '@/utils/filters';
const projectStore = useProjectStore();
const route = useRoute();

// --- 接收父组件参数定义 ---
interface Props {
  searchDialog: boolean;
  zdList?: any[]; // 如果你有具体类型，可以替换 any
  isManager?: boolean;
  moduleIdPop?: string;
  ifTree?: boolean;
  isKJ?: boolean;
  ruleTree?: any[]; // 同样建议替换 any
  ruleIds?: any[];
}

// 提供默认值
const props = withDefaults(defineProps<Props>(), {
  searchDialog: false,
  zdList: () => [],
  isManager: false,
  moduleIdPop: '',
  ifTree: false,
  isKJ: false,
  ruleTree: () => [],
  ruleIds: () => []
});
// 搜索弹窗副本
const searchDialogCopy = computed(() => props.searchDialog);

// --- emit ---
const emit = defineEmits<{
  (e: 'getChooseData', list: any[]): void;
  (e: 'closeSearchDialog'): void;
}>();

// 获取模块id
const moduleId = computed(() => {
  return props.moduleIdPop || projectStore.proModuleId;
});

// --- 变量定义 ---
const fullscreenLoading = ref(false);
const parcelList = ref([]); //数据列表
const total = ref(0);
const multipleSelection: any = ref([]);
const dialogSearch: any = ref({
  //弹窗筛选
  areaCode: '', //行政区划
  createDate: '', //创建时间
  optUserId: '', //最后修改时间
  createUserId: '', //采集人
  updateUserId: '', //最后修改人
  taskId: '', //任务id
  allocation: '', //查询是否分配任务 如果选择了任务设置为true
  pageNum: 1,
  pageSize: 10,
  parcelName: '',
  moduleId: moduleId.value, // 模块id
  conditionFields: [], //字段查询
  ruleIds: [],
  parcelCode: null
});
const shaixuanDialog = ref(false); //筛选弹窗
const taskList = ref([]); //任务列表
const isShowSearch = ref(false); //是否展示筛选内容
const isInit = ref(true); //是否允许初始化，反显已选择的数据
const defaultProps = reactive({
  children: 'list',
  label: 'parcelName'
});
const childNode = ref([]); //选中的子集id集合
const batchDialog = ref(false); //批量选择节点弹窗
const tree = ref([]);
const defaultPropsTree = reactive({
  children: 'list',
  label: 'typeName'
});
const batchChooseList = ref([]); //批量选择节点集合
const checkTreeList = ref([]); //当前选中的树结点的数据

// --- 虚拟树相关变量 ---
const treeData = ref<any[]>([]); // 树形数据
const defaultCheckedKeys = ref<string[]>([]); // 默认选中的节点keys
const treeProps = reactive({
  value: 'id',
  label: 'parcelName',
  children: 'children'
});

// --- ref ---
const multipleTableRef = ref();
const treeRef = ref();
const treeV2Ref = ref();
watch(
  () => searchDialogCopy.value,
  (val) => {
    if (val) {
      multipleSelection.value = props.zdList;
      if (props.moduleIdPop) {
        dialogSearch.value.moduleId = props.moduleIdPop;
      } else {
        dialogSearch.value.moduleId = projectStore.proModuleId;
      }
      getData(dialogSearch.value);
      getTree();
    }
  },
  { deep: true }
);

// --- 方法定义 ---
const getTree = () => {
  selectRules({ moduleId: moduleId.value }).then((res) => {
    if (res.code == 200) {
      tree.value = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 提交筛选
const submit = () => {
  const map = new Map();
  const arr = multipleSelection.value.filter((v) => !map.has(v.id) && map.set(v.id, v));
  // 当多次进入页数的时候和切换分页显示的时候 会有重复数据，所以需要数组去重一下
  emit('getChooseData', arr);
  multipleSelection.value = [];
  isInit.value = true;
  multipleTableRef.value.clearSelection();
};

// 关闭筛选
const handleClose = () => {
  emit('closeSearchDialog');
  //弹窗筛选
  dialogSearch.value = {
    areaCode: '', //行政区划
    createDate: '', //创建时间
    updateDate: '', //最后修改时间
    createUserId: '', //采集人
    optUserId: '', //最后修改人
    taskId: '', //任务id
    allocation: '', //查询是否分配任务 如果选择了任务设置为true
    pageNum: 1,
    pageSize: 10,
    parcelName: '',
    moduleId: moduleId.value,
    conditionFields: [], //字段查询
    ruleIds: [],
    parcelCode: null,
    express: false //是否查询表达式异常的数据
  };
  multipleSelection.value = [];
  multipleTableRef.value.clearSelection();
  isInit.value = true;
};

/**
 * 获取数据
 * @param parmas 参数
 */
const getData = (parmas: any) => {
  fullscreenLoading.value = true;

  // 虚拟树始终使用平铺数据，不需要树形结构
  parmas.ifTree = false;

  if (props.isKJ) {
    parmas.needKj = true;
  }
  parmas.levelNum = 1;
  if (route.query.iscq) {
    //如果是拆迁，需要把安置房数据刨除掉
    const ruleIds = [];
    props.ruleTree.forEach((v) => {
      if (!v.typeName.includes('安置房小区名称')) {
        ruleIds.push(v.id);
      }
    });
    parmas.ruleIds = ruleIds;
    delete parmas.levelNum;
  }
  if (props.ruleIds && route.query.isAZF) {
    //房源销控专用 多个模块相同节点的数据查询 需要把moduleId删除
    delete parmas.moduleId;
    delete parmas.levelNum;
    parmas.ruleIds = props.ruleIds;
  }
  getPlaceList(parmas).then((res) => {
    fullscreenLoading.value = false;
    if (res.code == 200) {
      parcelList.value = res.data.list;
      total.value = res.data.total;
      // 转换为树形数据（只保留第一级，子节点按需加载）
      treeData.value = convertToTreeData(res.data.list);
      if (isInit.value) {
        // 设置默认选中的节点
        defaultCheckedKeys.value = multipleSelection.value.map((item) => item.id);
        isInit.value = false;
      }
      // 请求结束 看queryParams里面的downLoadId是否有值 有值代表是需要下载未匹配到的数据
      if (parmas.downLoadId) {
        downloadNoMappingData(parmas.downLoadId);
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 导出未匹配的数据
 * @param id
 */
const downloadNoMappingData = (id) => {
  const params = {
    id: id,
    suffix: 'txt',
    fileName: '未匹配id',
    del: 1
  };
  findAsyncFileTemp(params).then((res) => {
    if (res.status == 200) {
      if (res.data.type == 'application/json') {
        //导出异常
        const read = new FileReader();
        read.readAsText(res.data, 'utf-8');
        let bugMsg = '';
        read.onload = (data) => {
          bugMsg = JSON.parse(data.currentTarget['result']).msg;
          ElMessage.error(JSON.parse(data.currentTarget['result']).msg);
          if (!bugMsg) {
            bugMsg = '未知异常';
          }
        };
      } else {
        //导出正常
        const name = decodeURI(res.headers['content-disposition']);
        const index = name.indexOf('=');
        const endFileName = name.substring(index + 1, name.length) || '未匹配数据.txt';
        const blob = new Blob([res.data], { type: 'text/plain' });

        // 针对ie浏览器
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(blob, endFileName);
        } else {
          //非ie浏览器
          const downloadElement = document.createElement('a');
          const href = window.URL.createObjectURL(blob); //常见下载的链接
          downloadElement.href = href;
          downloadElement.download = endFileName; //下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); //点击下载
          document.body.removeChild(downloadElement); //下载完成移除元素
          window.URL.revokeObjectURL(href); //释放blob对象
        }
      }
    }
  });
};

/**
 * 分页大小改变
 * @param val
 */
const handleSizeChange = (val) => {
  dialogSearch.value.pageSize = val;
  dialogSearch.value.pageNum = 1;
  submitSearch();
};

/**
 * 分页页码改变
 * @param val
 */
const handleCurrentChange = (val) => {
  dialogSearch.value.pageNum = val;
  submitSearch();
};

/**
 * 全选所有数据
 * @param val
 */
const handleSeletionAll = (val) => {
  if (val.length > 0) {
    // 全选
    val.forEach((item) => {
      item.delFlag = 1;
      if (item.list.length > 0) {
        const isLag = val.length > 0;
        handleSelectTableDel(item.list, isLag);
      }
      multipleSelection.value.push(item);
    });
  } else {
    //  非全选
    multipleSelection.value = [];
  }
};

/**
 * 单行选择
 * @param selection
 * @param row
 */
const handleSelectTable = (selection, row) => {
  // 如果 selected =true  代表选中节点，0 或者false 代表未选中节点
  const selected = selection.length && selection.indexOf(row) !== -1;
  if (selected) {
    // 选中
    row.delFlag = 1;
    row.list.map((item) => {
      if (item.list.length > 0) {
        handleSelectTableDel(item.list, selected);
      }
      nextTick(() => {
        checkTreeList.value.push(item.id);
        treeRef.value.setCheckedKeys(checkTreeList.value);
      });
      item.delFlag = 1;

      return item;
    });
    multipleSelection.value.push(row);
  } else {
    row.delFlag = 0;
    row.list.map((item) => {
      if (item.list.length > 0) {
        handleSelectTableDel(item.list, selected);
      }
      nextTick(() => {
        const index = checkTreeList.value.indexOf(item.id);
        checkTreeList.value.splice(index, 1);
        treeRef.value.setCheckedKeys(checkTreeList.value);
      });

      item.delFlag = 0;

      return item;
    });
    const ind = multipleSelection.value.indexOf(row);
    multipleSelection.value.splice(ind, 1);
  }
};

/**
 * 变量下级数据
 * @param list 数据列表
 * @param selected 是否选中
 */
const handleSelectTableDel = (list, selected) => {
  if (selected) {
    return list.map((ite) => {
      nextTick(() => {
        checkTreeList.value.push(ite.id);
        treeRef.value.setCheckedKeys(checkTreeList.value);
      });
      ite.delFlag = 1;
      if (ite.list.length > 0) {
        handleSelectTableDel(ite.list, ite);
      }
      return ite;
    });
  } else {
    list.map((ite) => {
      if (ite.list.length > 0) {
        handleSelectTableDel(ite.list, selected);
      }
      nextTick(() => {
        const index = checkTreeList.value.indexOf(ite.id);
        checkTreeList.value.splice(index, 1);
        treeRef.value.setCheckedKeys(checkTreeList.value);
      });
      ite.delFlag = 0;
      return ite;
    });
  }
};

/**
 * 关闭筛选弹窗
 */
const handleCloseShaixuan = () => {
  shaixuanDialog.value = false;
};

/**
 * 重置筛选
 */
const handleResetSearch = () => {
  dialogSearch.value = {
    areaCode: '', //行政区划
    createDate: '', //创建时间
    updateDate: '', //最后修改时间
    createUserId: '', //采集人
    optUserId: '', //最后修改人
    taskId: '', //任务id
    allocation: '', //查询是否分配任务 如果选择了任务设置为true
    pageNum: 1,
    pageSize: 10,
    parcelName: '',
    moduleId: moduleId.value,
    conditionFields: [], //字段查询
    express: false //是否查询表达式异常的数据
  }; //弹窗筛选
  isShowSearch.value = false;
  getData(dialogSearch.value);
  shaixuanDialog.value = false;
};

/**
 * 提交筛选条件
 * @param isMapping 是否需要下载
 */
const submitSearch = (isMapping?: number) => {
  if (isMapping == 1) {
    const downLoadId = (Math.random() + new Date().getTime()).toString(32).slice(0, 8);
    dialogSearch.value.downLoadId = downLoadId;
  } else {
    delete dialogSearch.value.downLoadId;
  }
  if (dialogSearch.value.parcelCode && !isArray(dialogSearch.value.parcelCode)) {
    dialogSearch.value.parcelCode = dialogSearch.value.parcelCode.split(',');
  }
  if (dialogSearch.value.taskId) {
    //选择了任务id 就要改变这个值
    dialogSearch.value.allocation = true;
  } else if (!dialogSearch.value.taskId) {
    dialogSearch.value.allocation = '';
  }
  if (dialogSearch.value.createDate && dialogSearch.value.createDate.length != 0) {
    dialogSearch.value.createTimeStart = dialogSearch.value.createDate[0];
    dialogSearch.value.createTimeEnd = dialogSearch.value.createDate[1];
  } else if (!dialogSearch.value.createDate) {
    dialogSearch.value.createTimeStart = '';
    dialogSearch.value.createTimeEnd = '';
  }
  if (dialogSearch.value.updateDate && dialogSearch.value.updateDate.length != 0) {
    dialogSearch.value.updateTimeStart = dialogSearch.value.updateDate[0];
    dialogSearch.value.updateTimeEnd = dialogSearch.value.updateDate[1];
  } else if (!dialogSearch.value.updateDate) {
    dialogSearch.value.updateTimeStart = '';
    dialogSearch.value.updateTimeEnd = '';
  }
  // 这一步非常重要，需要重新组装字段筛选条件
  // 重新组装筛选条件
  const conditionFields = [];
  const itemParmas = JSON.parse(JSON.stringify(dialogSearch.value));
  if (itemParmas.conditionFields && itemParmas.conditionFields.length != 0) {
    itemParmas.conditionFields.forEach((v, idx) => {
      conditionFields.push(v);
      // 条件取下一个的条件 最后一个不进入/
      if (idx != itemParmas.conditionFields.length - 1) {
        const obj = { type: 2, value: itemParmas.conditionFields[idx + 1].relation };
        conditionFields.push(obj);
      }
    });
    if (conditionFields.length != 0 && conditionFields[conditionFields.length - 1].type == 2) {
      //如果最后一个是条件 删除
      conditionFields.pop();
    }
    itemParmas.conditionFields = conditionFields;
  }
  getData(itemParmas);
  const val = dialogSearch.value;
  if (val.areaCode || val.createDate || val.optUserId || val.createUserId || val.updateUserId || val.taskId || val.parcelName) {
    isShowSearch.value = true;
  } else {
    isShowSearch.value = false;
  }
  shaixuanDialog.value = false;
};

/**
 * 清除选中的用户 1采集人 2最后更新人
 * @param type
 */
const clearUser = (type) => {
  if (type == 1) {
    dialogSearch.value.createUserId = '';
    dialogSearch.value.createUserName = '';
  } else {
    dialogSearch.value.optUserId = '';
    dialogSearch.value.optUserName = '';
  }
};

/**
 * 查询任务列表
 */
const getSearchTaskList = async () => {
  const params = {
    pageSize: 1000,
    pageNum: 1,
    pageType: 3,
    moduleId: dialogSearch.value.moduleId
  };
  if (props.moduleIdPop) {
    params.moduleId = props.moduleIdPop;
  }
  await getSearchTask(params).then((res) => {
    if (res.code == 200) {
      taskList.value = res.data.list;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 查询任务按钮
 */
const searchBtn = async () => {
  shaixuanDialog.value = true;
  getSearchTaskList();
};

/**
 * 删除数据提交按钮
 */
const submitDel = () => {
  if (multipleSelection.value.length == 0 && childNode.value.length == 0) {
    ElMessage.error('请选择您要删除的数据！！！');
    return;
  }
  ElMessageBox.confirm('确定要删除已选择的数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  })
    .then(() => {
      ElMessageBox.confirm('该操作会彻底删除数据，数据将无法找回，请谨慎操作！！！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        const list = [];
        const map = new Map();
        multipleSelection.value
          .filter((v) => !map.has(v.id) && map.set(v.id, v))
          .forEach((v) => {
            list.push({
              id: v.id,
              delFlag: 1,
              taskId: 0,
              ruleId: v.ruleId
            });
          });
        childNode.value.forEach((v) => {
          list.push({
            id: v.id,
            delFlag: 1,
            taskId: 0,
            ruleId: v.ruleId
          });
        });
        operaParcel(list).then((res) => {
          if (res.code == 200) {
            ElMessage({
              type: 'success',
              message: '删除成功!'
            });
            getData(dialogSearch.value);
            multipleSelection.value = [];
            multipleTableRef.value.clearSelection();
            childNode.value = [];
          } else {
            ElMessage.error(res.msg);
          }
        });
      });
    })
    .catch(() => {});
};

/**
 * 修改筛选条件
 * @param type 1新增 2删除
 * @param idx 删除的下标
 */
const editCondition = (type, idx) => {
  //type 1新增 2删除 idx 删除的下标
  if (type == 1) {
    dialogSearch.value.conditionFields.push({
      name: '', //字段名字
      operator: '=',
      value: '',
      index: '',
      relation: 'and', // 关系 and or
      type: 1, // 1字段 2关系
      linkId: undefined
    });
  } else {
    dialogSearch.value.conditionFields.splice(idx, 1);
  }
};

/**
 * 节点选中
 * @param data 节点数据
 * @param flg 是否选中
 */
// const handleNodeClick = (data, flg) => {
//   if (flg) {
//     //选中
//     const isHas = checkTreeList.value.indexOf(data.id) == -1;
//     if (isHas) {
//       //  不存在才推送
//       checkTreeList.value.push(data.id);
//       childNode.value.push({ id: data.id, ruleId: data.ruleId });
//     }

//     if (data.list.length > 0) {
//       //  变量取消所有节点
//       data.list.forEach((item) => {
//         handleNodeClick(item, true);
//       });
//     }
//     treeRef.value.setCheckedKeys(checkTreeList.value);
//   } else {
//     for (let i = 0; i < childNode.value.length; i++) {
//       if (childNode.value[i].id == data.id) {
//         childNode.value.splice(i, 1);
//         break;
//       }
//     }
//     // 取消反选
//     data.delFlag = 0;
//     const index = checkTreeList.value.indexOf(data.id);
//     checkTreeList.value.splice(index, 1);
//     if (data.list.length > 0) {
//       data.list.forEach((item) => {
//         handleNodeClick(item, false);
//       });
//     }
//     treeRef.value.setCheckedKeys(checkTreeList.value);
//   }
// };

const handleRowClick = (row, index, e) => {
  multipleTableRef.value.toggleRowExpansion(row);
};

/**
 * 批量选择树节点
 */
const batchNode = () => {
  if (multipleSelection.value.length == 0) {
    ElMessage.error('请至少选择一条数据！！！');
    return;
  }
  batchDialog.value = true;
};

/**
 * 关闭批量选择节点
 */
const handleCloseBatch = () => {
  batchDialog.value = false;
};

/**
 * 提交批量选择节点
 */
const submitBatch = () => {};

/**
 * 节点选中
 * @param data 节点数据
 * @param flg 是否选中
 */
const handleCheckChange = (data, flg) => {};

const columns = ref([
  {
    key: 'selection',
    dataKey: 'selection',
    title: '',
    width: 55,
    headerCellRenderer: () => {
      return h(ElCheckbox, {
        modelValue: parcelList.value.length > 0 && multipleSelection.value.length === parcelList.value.length,
        indeterminate: multipleSelection.value.length > 0 && multipleSelection.value.length < parcelList.value.length,
        onChange: (val) => {
          if (val) {
            // 全选
            multipleSelection.value = [...parcelList.value];
            parcelList.value.forEach((item) => {
              item.delFlag = 1;
              if (item.list?.length > 0) {
                handleSelectTableDel(item.list, true);
              }
            });
          } else {
            // 取消全选
            multipleSelection.value = [];
            parcelList.value.forEach((item) => {
              item.delFlag = 0;
              if (item.list?.length > 0) {
                handleSelectTableDel(item.list, false);
              }
            });
          }
        }
      });
    },
    cellRenderer: ({ rowData }) => {
      return h(ElCheckbox, {
        modelValue: multipleSelection.value.some((item) => item.id === rowData.id),
        onChange: (val) => {
          handleSelectTable(val ? [rowData] : [], rowData);
        }
      });
    }
  },
  {
    key: 'index',
    dataKey: 'index',
    title: '序号',
    width: 70,
    cellRenderer: ({ rowIndex }) => {
      return h('span', null, rowIndex + 1);
    }
  },
  {
    key: 'parcelName',
    dataKey: 'parcelName',
    title: '数据名称',
    width: 400
  },
  {
    key: 'createTime',
    dataKey: 'createTime',
    title: '创建时间',
    width: 299,
    cellRenderer: ({ rowData }) => {
      return h('span', null, formatDateType(rowData.createTime));
    }
  }
]);

/**
 * 转换数据为树形结构（只保留第一级，子节点按需加载）
 * @param list 平铺数据列表
 */
const convertToTreeData = (list: any[]) => {
  return list.map((item) => ({
    ...item,
    id: item.id || `node-${Math.random().toString(36).substr(2, 9)}`,
    children: [], // 初始为空，点击时才加载
    loaded: false, // 标记是否已加载过子节点
    levelNum: 1 // 第一级数据都是level 1
  }));
};

/**
 * 获取节点索引
 * @param data 节点数据
 */
const getNodeIndex = (data: any) => {
  const flatList = flattenTreeData(treeData.value);
  return flatList.findIndex((item) => item.id === data.id) + 1;
};

/**
 * 扁平化树形数据
 * @param treeData 树形数据
 */
const flattenTreeData = (treeData: any[]): any[] => {
  const result: any[] = [];
  const traverse = (nodes: any[]) => {
    nodes.forEach((node) => {
      result.push(node);
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(treeData);
  return result;
};

/**
 * 判断节点是否可以加载子节点
 * @param data 节点数据
 */
const canLoadChildren = (data: any) => {
  return !data.loaded && data.levelNum < 10; // 假设最多10级
};

/**
 * 处理树节点点击（真正的按需请求）
 * @param data 节点数据
 * @param node 节点对象
 */
const handleNodeClick = async (data: any, node: any) => {
  // 检查是否是"加载更多"节点
  if (data.isLoadMore) {
    await loadMoreChildren(data, node);
    return;
  }

  // 如果已经加载过，不重复请求
  if (data.loaded) {
    return;
  }

  // 发起API请求获取子节点数据
  await loadChildrenFromAPI(data);
};

/**
 * 从API加载子节点数据（真正的按需请求）
 * @param parentNode 父节点数据
 */
const loadChildrenFromAPI = async (parentNode: any) => {
  try {
    // 构建请求参数（参考zdList.vue的loadChildren方法）
    const params = {
      id: parentNode.id,
      pageNum: 1,
      pageSize: 20
    };
    // 调用selectParcelOneList接口获取子节点列表
    const resp = await selectParcelOneList(params);

    if (resp.code === 200 && resp.data) {
      // 检查是否有子节点数据
      if (resp.data.list && resp.data.list.length > 0) {
        // 转换子节点数据
        const childrenData = resp.data.list.map((item: any) => ({
          ...item,
          id: item.id || `node-${Math.random().toString(36).substr(2, 9)}`,
          children: [],
          loaded: false,
          levelNum: parentNode.levelNum + 1
        }));

        // 设置子节点
        parentNode.children = childrenData;
        parentNode.loaded = true;
        parentNode.total = resp.data.total;
        parentNode.pageNum = 1;
        parentNode.pageSize = 20;

        // 判断是否需要添加"加载更多"节点（参考zdList.vue的逻辑）
        if (resp.data.total > 20) {
          parentNode.children.push({
            parcelName: '加载更多',
            id: `${parentNode.id}_loadmore`,
            isLoadMore: true,
            parentId: parentNode.id,
            pageNum: 2,
            pageSize: 20,
            levelNum: parentNode.levelNum + 1
          });
        }
      } else {
        // 没有子节点数据
        parentNode.loaded = true;
      }

      // 更新树形数据以触发重新渲染
      treeData.value = [...treeData.value];
    } else {
      // API调用失败
      parentNode.loaded = true;
    }
  } catch (error) {
    console.error('Error loading children:', error);
    ElMessage.error('加载子节点失败');
    parentNode.loaded = true;
  }
};

/**
 * 加载更多子节点数据（参考zdList.vue的实现）
 * @param loadMoreNode 加载更多节点的数据
 * @param node 加载更多节点
 */
const loadMoreChildren = async (loadMoreNode: any, node: any) => {
  try {
    // 找到父节点
    const parentNode = findNodeById(treeData.value, loadMoreNode.parentId);
    if (!parentNode) {
      ElMessage.error('找不到父节点');
      return;
    }

    // 构建请求参数
    const params = {
      id: loadMoreNode.parentId,
      pageNum: loadMoreNode.pageNum,
      pageSize: 20
    };

    // 调用API获取更多子节点数据
    const resp = await selectParcelOneList(params);

    if (resp.code === 200 && resp.data) {
      // 移除"加载更多"节点
      const loadMoreIndex = parentNode.children.findIndex((child: any) => child.isLoadMore);
      if (loadMoreIndex !== -1) {
        parentNode.children.splice(loadMoreIndex, 1);
      }

      // 转换并添加新的子节点
      if (resp.data.list && resp.data.list.length > 0) {
        const newChildren = resp.data.list.map((item: any) => ({
          ...item,
          id: item.id || `node-${Math.random().toString(36).substr(2, 9)}`,
          children: [],
          loaded: false,
          levelNum: parentNode.levelNum + 1
        }));

        parentNode.children.push(...newChildren);
        parentNode.pageNum = loadMoreNode.pageNum;

        // 判断是否还需要"加载更多"节点
        if (loadMoreNode.pageNum < resp.data.pages) {
          parentNode.children.push({
            parcelName: '加载更多',
            id: `${loadMoreNode.parentId}_loadmore`,
            isLoadMore: true,
            parentId: loadMoreNode.parentId,
            pageNum: loadMoreNode.pageNum + 1,
            pageSize: 20,
            levelNum: parentNode.levelNum + 1
          });
        }
      }

      // 更新树形数据以触发重新渲染
      treeData.value = [...treeData.value];
    }
  } catch (error) {
    console.error('Error loading more children:', error);
    ElMessage.error('加载更多失败');
  }
};

/**
 * 根据ID查找节点
 * @param nodes 节点数组
 * @param id 节点ID
 */
const findNodeById = (nodes: any[], id: string): any => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

/**
 * 判断节点是否被选中
 * @param data 节点数据
 */
const isNodeChecked = (data: any) => {
  return multipleSelection.value.some((item) => item.id === data.id);
};

/**
 * 处理单个节点的复选框变化
 * @param data 节点数据
 * @param checked 是否选中
 */
const handleNodeCheck = (data: any, checked: any) => {
  const isChecked = Boolean(checked);
  if (isChecked) {
    // 添加到选中列表
    if (!multipleSelection.value.some((item: any) => item.id === data.id)) {
      multipleSelection.value.push(data);
    }
  } else {
    // 从选中列表移除
    const index = multipleSelection.value.findIndex((item: any) => item.id === data.id);
    if (index !== -1) {
      multipleSelection.value.splice(index, 1);
    }
  }

  // 更新默认选中的keys
  defaultCheckedKeys.value = multipleSelection.value.map((item: any) => item.id);
};

/**
 * 处理树节点选中（保留原有方法以防其他地方使用）
 * @param data 节点数据
 * @param info 选中信息
 */
const handleTreeCheck = (data: any, info: any) => {
  multipleSelection.value = info.checkedNodes;
};
</script>
<style lang="scss" scoped>
.dialog-search {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.searchData-main {
  width: 100%;
  height: 100%;
}
.dialog-content {
  height: 485px;
  width: 100%;
  overflow: auto;
  /*滚动条样式*/
  &::-webkit-scrollbar {
    width: 10px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
  :deep(.el-table-v2__header) {
    background-color: var(--el-fill-color-light);
    border-bottom: 1px solid var(--el-border-color-lighter);

    .el-table-v2__header-cell {
      font-weight: 600;
      color: var(--el-text-color-primary);
      padding: 8px 12px;
    }
  }

  :deep(.el-table-v2__row) {
    transition: background-color 0.2s ease;
    border-bottom: 1px solid var(--el-border-color-lighter);

    &:hover {
      background-color: var(--el-fill-color-light);
    }

    &.selected-row {
      background-color: var(--el-color-primary-light-9);

      &:hover {
        background-color: var(--el-color-primary-light-8);
      }
    }
  }

  :deep(.el-checkbox) {
    .el-checkbox__inner {
      border-radius: 2px;
      transition: all 0.2s;
    }

    &.is-checked {
      .el-checkbox__inner {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }
    }
  }

  // 虚拟树样式
  :deep(.el-tree-v2) {
    .el-tree-node {
      border-radius: 4px;
      margin: 1px 0;

      &:hover {
        background-color: transparent;
      }

      // 选中状态
      &.is-checked {
        .tree-node-content {
          background-color: rgba(64, 158, 255, 0.1) !important;
          border-left: 3px solid var(--el-color-primary) !important;

          .node-name {
            color: var(--el-color-primary) !important;
            font-weight: 600 !important;
          }
        }
      }
    }
  }
}

// 树节点内容样式
.tree-node-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 0;
  position: relative;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--el-fill-color-light);
  }

  // 不同层级的背景色
  &.level-1 {
    background-color: rgba(64, 158, 255, 0.02);
    border-left: 3px solid var(--el-color-primary);

    &:hover {
      background-color: rgba(64, 158, 255, 0.05);
    }
  }

  &.level-2 {
    background-color: rgba(103, 194, 58, 0.02);

    &:hover {
      background-color: rgba(103, 194, 58, 0.05);
    }
  }

  &.level-3 {
    background-color: rgba(230, 162, 60, 0.02);

    &:hover {
      background-color: rgba(230, 162, 60, 0.05);
    }
  }

  &.level-4,
  &.level-5,
  &.level-6,
  &.level-7,
  &.level-8,
  &.level-9,
  &.level-10 {
    background-color: rgba(144, 147, 153, 0.01);

    &:hover {
      background-color: rgba(144, 147, 153, 0.03);
    }
  }

  // 层级连接线
  .level-lines {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    pointer-events: none;

    .level-line {
      position: absolute;
      top: 0;
      width: 1px;
      height: 100%;
      background-color: var(--el-border-color);
      opacity: 0.6;
    }
  }

  // 展开图标区域
  .node-expand {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    cursor: pointer;
    border-radius: 2px;
    transition: background-color 0.2s;

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }

    .expand-icon {
      font-size: 12px;
      color: var(--el-color-info);
      transition:
        transform 0.2s,
        color 0.2s;

      &:hover {
        color: var(--el-color-primary);
      }

      // 展开状态时旋转90度
      &.expanded {
        transform: rotate(90deg);
        color: var(--el-color-primary);
      }
    }
  }

  .node-expand-placeholder {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }

  // 复选框
  .node-checkbox {
    margin-right: 8px;
    display: flex;
    align-items: center;
  }

  // 序号
  .node-index {
    width: 60px;
    text-align: center;
    color: var(--el-text-color-regular);
    font-size: 12px;
    margin-right: 12px;
  }

  // 节点名称（不同层级不同样式）
  .node-name {
    flex: 1;
    color: var(--el-text-color-primary);
    font-weight: 500;
    margin-right: 12px;

    // 第一级 - 最重要
    &.level-1 {
      font-weight: 600;
      font-size: 14px;
      color: var(--el-color-primary);
    }

    // 第二级
    &.level-2 {
      font-weight: 500;
      font-size: 13px;
      color: var(--el-text-color-primary);
    }

    // 第三级
    &.level-3 {
      font-weight: 400;
      font-size: 13px;
      color: var(--el-text-color-regular);
    }

    // 第四级及以上
    &.level-4,
    &.level-5,
    &.level-6,
    &.level-7,
    &.level-8,
    &.level-9,
    &.level-10 {
      font-weight: 400;
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }

  // 时间
  .node-time {
    width: 280px;
    text-align: right;
    color: var(--el-text-color-regular);
    font-size: 12px;
  }

  // 加载更多节点样式
  &.load-more-node {
    background-color: rgba(64, 158, 255, 0.05) !important;
    border: 1px dashed var(--el-color-primary);
    border-radius: 4px;
    margin: 2px 0;
    cursor: pointer;

    &:hover {
      background-color: rgba(64, 158, 255, 0.1) !important;
      border-color: var(--el-color-primary);
    }

    .load-more-content {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      color: var(--el-color-primary);
      font-weight: 500;
      gap: 8px;
      .loading-icon {
        animation: rotate 1s linear infinite;
      }

      .load-more-text {
        font-size: 13px;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.page {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
